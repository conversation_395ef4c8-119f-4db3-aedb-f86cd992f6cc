from .recipe_agent.agent import recipe_agent
from google.adk.tools import agent_tool
from google.adk.tools import ToolContext
from google.adk.agents import Agent
import logging
from chromadb.utils import embedding_functions
import json
import chromadb
import os
import sys
__import__('pysqlite3')
sys.modules['sqlite3'] = sys.modules.pop('pysqlite3')


# --- Load Configuration & Initialize Client ONCE ---
# This code runs only once when the agent service starts, making it very efficient.
logging.info("Initializing ChromaDB client and embedding function...")
CHROMA_HOST = os.getenv("CHROMA_HOST", "chroma")
CHROMA_PORT = os.getenv("CHROMA_PORT", 8000)
CHROMA_COLLECTION = os.getenv("CHROMA_COLLECTION", "products")
EMBEDDING_MODEL = os.getenv("EMBEDDING_MODEL", "all-MiniLM-L6-v2")

client = chromadb.HttpClient(host=CHROMA_HOST, port=CHROMA_PORT)
sentence_transformer_ef = embedding_functions.SentenceTransformerEmbeddingFunction(model_name=EMBEDDING_MODEL)
collection = client.get_or_create_collection(name=CHROMA_COLLECTION, embedding_function=sentence_transformer_ef)
logging.info("ChromaDB client initialized successfully.")

recipe_agent_tool = agent_tool.AgentTool(agent=recipe_agent)


def search_liquor_store_products(queries: list[str], tool_context: ToolContext) -> list[dict]:
    """
    Searches for products available in the liquor store based on a list of user search terms.
    This is the primary tool to use when a user asks for any kind of product recommendation.

    Args:
        queries: A list of search terms describing what the user is looking for. 
                 For example: ["a smoky Islay scotch", "something cheap for a margarita"]

    Returns:
        A list of dictionary objects, where each dictionary represents a unique product found. 
        Each product dictionary contains details like name, brand, price, and description.
    """
    print("----- Tool: search_liquor_store_products -----")
    print(f"Received queries: {queries}")

    # Use a dictionary to store found items, using the item's ID as the key.
    # This automatically handles duplicates if different queries return the same product.
    found_items_by_id = {}

    for query in queries:
        print(query, "query")
        try:
            results = collection.query(
                query_texts=[query],
                n_results=10,  # Fetch top 3 results for each query
                where={"visibility": "visible"}  # CRUCIAL: Only find products that are in stock
            )
            # print(f"results debug: {results}")

            # Process the results from this query
            if results and results['ids'] and results['ids'][0]:
                for i, item_id in enumerate(results['ids'][0]):
                    if item_id not in found_items_by_id:
                        metadata = results['metadatas'][0][i]

                        # Parse JSON strings in metadata
                        def safe_json_parse(json_str):
                            if isinstance(json_str, str):
                                try:
                                    return json.loads(json_str)
                                except (json.JSONDecoderError, ValueError):
                                    return json_str
                            return json_str

                        # Parse nested JSON Fields
                        size_data = safe_json_parse(metadata.get('size', '{}'))
                        images_data = safe_json_parse(metadata.get('images', '[]'))
                        product_insight = safe_json_parse(metadata.get('productInsight', '{}'))
                        pack_size_detail = safe_json_parse(metadata.get('packSizeDetail', '{}'))

                        # Format a clean product dictionary for the agent
                        product_info = {
                            "id": item_id,
                            "upc": item_id,
                            "item_name": metadata.get('db_item_name'),
                            "name": metadata.get('name'),
                            "brandName": metadata.get('brandName'),
                            "sale_price": metadata.get('db_sale_price'),
                            "size": size_data.get('sizeValue') if isinstance(size_data, dict) else None,
                            "main_category": metadata.get('db_main_category'),
                            "description": metadata.get('description'),
                            "images": images_data if isinstance(images_data, list) else [],
                            "productInsight": product_insight if isinstance(product_insight, dict) else {},
                            "pairingInfo": metadata.get('pairingInfo'),
                            "alcoholeContent": product_insight.get('alcoholContent') if isinstance(product_insight, dict) else None,
                            "rating": product_insight.get('rating') if isinstance(product_insight, dict) else None,
                            "packSizeDetail": pack_size_detail if isinstance(pack_size_detail, dict) else {}
                        }
                        # print(f"metadata debug: {metadata}")
                        found_items_by_id[item_id] = product_info
        except Exception as e:
            print(f"Error during ChromaDB query for '{query}': {e}")
            # Continue to the next query even if one fails
            continue

    # Convert the dictionary of unique items back into a list
    final_items_list = list(found_items_by_id.values())

    print(f"Found {len(final_items_list)} unique items in total.")
    if final_items_list:
        print(f"Sample item structure: {final_items_list[0]}")
        print(
            f"Sample item keys: {final_items_list[0].keys() if isinstance(final_items_list[0], dict) else 'Not a dict'}")

    result = {
        "status": "success",
        "products": final_items_list
    }

    # tool_context.state["products_data"] = result
    # print(f"\n {final_items_list} .")

    print("---------------------------------------------")
    return result


def get_products_by_upcs(upcs: list[str], tool_context: ToolContext) -> dict:
    """
    Connects to a ChromaDB instance and retrieves multiple products by a list of UPCs in a single batch.

    Args:
        host (str): The hostname or IP address of the ChromaDB server.
        port (str): The port number for the ChromaDB server.
        collection_name (str): The name of the collection to query.
        upcs (list[str]): A list of UPCs (IDs) of the products to retrieve.

    Returns:
        dict: The product data retrieved from the collection, including metadatas.
              Returns None if the upcs list is empty or not provided.
    """
    print("----- Tool: get_products_by_upcs -----")
    if not upcs:
        print("Error: Please provide a list of UPCs.")
        return None

    print("UPC", upcs)

    try:
        # --- Connect and fetch ---
        client = chromadb.HttpClient(host=CHROMA_HOST, port=CHROMA_PORT)
        collection = client.get_collection(name=CHROMA_COLLECTION)

        # --- Get the items by their IDs (which are the UPCs) in a single batch ---
        raw_data = collection.get(
            ids=upcs,
            include=["metadatas", "documents"]
        )

        # Format the data like search_liquor_store_products does
        formatted_products = []
        if raw_data and raw_data.get('ids'):
            for i, item_id in enumerate(raw_data['ids']):
                metadata = raw_data['metadatas'][i]

                # Parse JSON strings in metadata
                def safe_json_parse(json_str):
                    if isinstance(json_str, str):
                        try:
                            return json.loads(json_str)
                        except (json.JSONDecodeError, ValueError):
                            return json_str
                    return json_str

                # Parse nested JSON Fields
                size_data = safe_json_parse(metadata.get('size', '{}'))
                images_data = safe_json_parse(metadata.get('images', '[]'))
                product_insight = safe_json_parse(metadata.get('productInsight', '{}'))
                pack_size_detail = safe_json_parse(metadata.get('packSizeDetail', '{}'))

                # Format a clean product dictionary
                product_info = {
                    "id": item_id,
                    "upc": item_id,
                    "item_name": metadata.get('db_item_name'),
                    "name": metadata.get('name'),
                    "brandName": metadata.get('brandName'),
                    "sale_price": metadata.get('db_sale_price'),
                    "size": size_data.get('sizeValue') if isinstance(size_data, dict) else None,
                    "main_category": metadata.get('db_main_category'),
                    "description": metadata.get('description'),
                    "images": images_data if isinstance(images_data, list) else [],
                    "productInsight": product_insight if isinstance(product_insight, dict) else {},
                    "pairingInfo": metadata.get('pairingInfo'),
                    "alcoholContent": product_insight.get('alcoholContent') if isinstance(product_insight, dict) else None,
                    "rating": product_insight.get('rating') if isinstance(product_insight, dict) else None,
                    "packSizeDetail": pack_size_detail if isinstance(pack_size_detail, dict) else {}
                }
                formatted_products.append(product_info)

            result = {
                "status": "success",
                "products": formatted_products
            }
        tool_context.state["products_data"] = result["products"]

        # products_data = tool_context.state["products_data"]
        # products_list = products_data["products"]  # This is the actual list

        # print("products_data debug", result)
        return result
    except Exception as e:
        print(f"Error retrieving products by UPCs: {e}")
        return {"status": "error", "message": str(e)}


instruction = """
You are a friendly and knowledgeable AI store assistant owner for a liquor and tobbaco and related products. Your persona is the store owner. Your primary goal is to provide excellent customer service through natural conversation and intelligent recommendations.
**// CORE DIRECTIVES //**

    **1. Persona and Language:**
    -   Act as a warm, friendly, and human store owner.
    -   If the user speaks a language other than English, you MUST respond in that same language.

    **2. Master Tool Strategy:**
    -   Your primary job is to determine the user's intent and select the correct tool.
        1. Use the product_expert_agent_tool for any requests related to products.
        2. Use the recipe_agent_tool for any requests related to recipes.
        4. The final curated list of products or recipes is available in the state as {products_data}, {recipe_data}
        5. Always provide the user a friendly response about your selection

**// TOOL-SPECIFIC INSTRUCTIONS //**

**A. `product_expert_agent_tool` - Rules for Product Queries:**

        1. First, use the search_liquor_store_products tool with 3-5 relevant search queries
            def search_liquor_store_products(queries: list[str], tool_context: ToolContext) -> list[dict]:
            Searches for products available in the liquor store based on a list of user search terms.
            This is the primary tool to use when a user asks for any kind of product recommendation.

            Args:
                queries: A list of search terms describing what the user is looking for. 
                        For example: ["a smoky Islay scotch", "something cheap for a margarita"]

            Returns:
                A list of dictionary objects, where each dictionary represents a unique product found. 
                Each product dictionary contains details like name, brand, price, and description.

    2. Then, from those results, select the 6-8 most relevant products
    3. Use get_products_by_upcs tool with the selected UPCs to get the final curated list

        def get_products_by_upcs(upcs: list[str], tool_context: ToolContext) -> dict:
            Connects to a ChromaDB instance and retrieves multiple products by a list of UPCs in a single batch.

            Args:
                host (str): The hostname or IP address of the ChromaDB server.
                port (str): The port number for the ChromaDB server.
                collection_name (str): The name of the collection to query.
                upcs (list[str]): A list of UPCs (IDs) of the products to retrieve.

            Returns:
                dict: The product data retrieved from the collection, including metadatas.
                    Returns None if the upcs list is empty or not provided.
**B. `recipe_agent_tool` - Rules for Recipe Queries:**

    *   **Action:** When you use this tool, it will automatically find a recipe and the data will be placed in the session state.
    *   **Your Response:**
        -   **Look for the recipe result in the session state under the key `{recipe_data}`**.
        -   Use this data to generate a friendly conversational message like: "I've just pulled up a great recipe for a [Drink Name] for you! It's a [description]. Let me know if you'd like help finding any of the ingredients."
    *   **If the tool fails (no data in session):** Respond with "I couldn't find a recipe for that. Could you try rephrasing?"

**3. Understanding Your Internal Data Flow:**
    -   This is how you receive and process information from your tools.
    1.  You decide to use a tool.
    2.  The system runs the tool for you.
    3.  The structured data result is automatically placed into your temporary memory, called the **session state**.
    4.  You will then be prompted to generate a conversational response. To do this, you **MUST refer to the data now available to you in the session state.**
    -   **Crucially, NEVER mention the words 'session state', 'key', `products_data`, in your response to the user.** This is your internal knowledge only.

**4. Generating a Conversational Response:**
    -   After a tool runs and the data is in your session state, the system will handle sending the structured data (like a recipe panel or product cards) to the user's interface.
    -   Your job is to provide the **conversational text** that accompanies that data by summarizing the information you find in the session state.
    -   **NEVER** output raw JSON or database terms like "stock_qty", "rows", or "item_name".
    -   Keep your responses concise and end with a question to continue the conversation.

    *   **Your Response (After a successful query):**
        -   **Look for the result in the session state under the key {products_data}**.
        -   The system will display the products to the user; your job is to create a conversational summary based on this data.
        -   Mention the product names and their prices. You MUST also mention the `location` of each product if available.
        -   **Crucially, NEVER mention the `stock_qty` (stock count) to the user.**
    *   **Your Response (If no products are found):**
        1.  Say: "I'm sorry, it looks like we're currently out of that. Let me check what other categories we have available..."
        2.  Then, immediately call `product_expert_agent_tool` again with the query "list all available main categories".
    *   **Your Response (After listing categories):**
        -   Look for the category list in the session state.
        -   Suggest 3-5 categories conversationally: "We have a great selection in categories like [Category1], [Category2], and [Category3]. Would you like to know more about any of these?"

** *** CRITICAL RULE: GROUNDING ***:**
-   When responding about products available in the store, you **MUST ONLY** use the information returned by the `product_expert_agent_tool`.
-   **DO NOT** use your own general knowledge about other wines, beers, or spirits. Your knowledge is limited to the inventory in this store's database.
-   If the tool returns a list of products, your conversational summary **MUST** be based *only* on that specific list.
- If data or partial data is unavailable, apologize and inform the user in a friendly manner. Do not make up information.
"""

root_agent = Agent(
    model='gemini-2.0-flash-live-001',
    name='root_agent',
    description=(
        'Shop agent for an liquor store'
    ),
    instruction=instruction,
    tools=[
        get_products_by_upcs,
        search_liquor_store_products,
        recipe_agent_tool,
    ],
)
