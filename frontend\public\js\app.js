import { startAudioPlayerWorklet } from "./audio-player.js";
import { startAudioRecorderWorklet } from "./audio-recorder.js";

// Global State for core chat logic
let currentSessionId = null;
let currentUserId = null;
let eventSource = null;
let audioPlayerNode, audioPlayerContext;
let audioRecorderNode, audioRecorderContext, micStream;
let audioBuffer = [];
let bufferTimer = null;
let isAudioMode = false;
let productLoaderTimeout = null;

// Suggested prompts data structure
const SUGGESTED_PROMPTS = {
  discovery: [
    "What are your most popular whiskey brands?",
    "Show me wines under $30",
    "What cocktail ingredients do you recommend?",
    "I'm looking for a gift for a whiskey lover",
  ],
  recommendations: [
    "Recommend a wine for dinner tonight",
    "What goes well with bourbon?",
    "Suggest cocktails for a party",
    "Find me something similar to Macallan",
  ],
  education: [
    "Tell me about different types of whiskey",
    "How do I make a perfect Old Fashioned?",
    "What's the difference between cognac and brandy?",
    "Explain wine tasting basics",
  ],
  pairing: [
    "What spirits pair with chocolate?",
    "Wine recommendations for steak",
    "Best whiskey for cigars",
    "Cocktails that go with appetizers",
  ],
};

// Session timeout variables
let sessionTimeoutTimer = null;
let lastActivityTime = Date.now();
const SESSION_TIMEOUT_DURATION = 1200000; // 12 minutes, converted in milliseconds

const AUDIO_BUFFER_SEND_INTERVAL = 200;
const SSE_RECONNECT_INTERVAL = 5000;

// DOM Elements (from dev_jaykin's index.html and general structure)
const messageInput = document.getElementById("message");
const sendButton = document.getElementById("sendButton");
const micButton = document.getElementById("micButton");
const messagesDiv = document.getElementById("messages");
const listeningIndicator = document.getElementById("listeningIndicator");
const recommendedProductsDiv = document.getElementById("recommendedProducts");
const rightPanel = document.getElementById("rightPanel");
const rightPanelHeadline = document.getElementById("rightPanelHeadline");
const leftPanel = document.querySelector(".left-panel");
const startChatBtn = document.getElementById("startChatBtn");
const fullscreenBtn = document.getElementById("fullscreenBtn");
const fullscreenIcon = document.getElementById("fullscreenIcon");
const suggestedPromptsContainer = document.getElementById(
  "suggestedPromptsContainer"
);
const suggestedPromptsGrid = document.getElementById("suggestedPromptsGrid");

let currentAgentMessageElement = null;
let currentUserMessageElement = null;

// Variables to handle chunked JSON data
let jsonBuffer = "";
let isReceivingJson = false;

/**
 * =================================================================
 * Suggested Prompts Functionality
 * =================================================================
 */

function initializeSuggestedPrompts() {
  // Show prompts initially when chat starts
  showSuggestedPrompts();

  // Hide prompts when user starts typing
  messageInput.addEventListener("input", () => {
    if (messageInput.value.trim().length > 0) {
      hideSuggestedPrompts();
    } else if (messagesDiv.children.length === 0) {
      // Show prompts again if input is empty and no messages
      showSuggestedPrompts();
    }
  });
}

function showSuggestedPrompts() {
  if (!suggestedPromptsContainer || !suggestedPromptsGrid) return;

  // Only show if there are no messages in the chat
  if (messagesDiv.children.length > 0) {
    hideSuggestedPrompts();
    return;
  }

  // Clear existing prompts
  suggestedPromptsGrid.innerHTML = "";

  // Get a random selection of prompts from different categories
  const selectedPrompts = getRandomPrompts();

  // Create prompt buttons
  selectedPrompts.forEach((prompt, index) => {
    const button = document.createElement("button");
    button.className = "suggested-prompt-button";
    button.textContent = prompt;
    button.setAttribute("data-prompt", prompt);

    // Add click handler
    button.addEventListener("click", () => handlePromptClick(prompt));

    suggestedPromptsGrid.appendChild(button);
  });

  // Show container
  suggestedPromptsContainer.classList.remove("hidden");
}

function hideSuggestedPrompts() {
  if (!suggestedPromptsContainer) return;
  suggestedPromptsContainer.classList.add("hidden");
}

const SUGGESTED_PROMPTS = {
  discovery: [
    "What are your most popular whiskey brands?",
    "Show me wines under $30",
    "What cocktail ingredients do you recommend?",
    "I'm looking for a gift for a whiskey lover",
  ],
  recommendations: [
    "Recommend a wine for dinner tonight",
    "What goes well with bourbon?",
    "Suggest cocktails for a party",
    "Find me something similar to Macallan",
  ],
  education: [
    "Tell me about different types of whiskey",
    "How do I make a perfect Old Fashioned?",
    "What's the difference between cognac and brandy?",
    "Explain wine tasting basics",
  ],
  pairing: [
    "What spirits pair with chocolate?",
    "Wine recommendations for steak",
    "Best whiskey for cigars",
    "Cocktails that go with appetizers",
  ],
};

function handlePromptClick(prompt) {
  // Populate the input field with the selected prompt
  messageInput.value = prompt;

  // Hide the suggested prompts
  hideSuggestedPrompts();

  // Focus on the input field and position cursor at the end
  messageInput.focus();
  messageInput.setSelectionRange(prompt.length, prompt.length);

  // Trigger auto-resize
  autoResize();

  // Track user activity
  trackUserActivity();
}

function stopAgentAudioPlayback() {
  if (audioPlayerNode) {
    // Send a command to the AudioWorklet processor to clear its buffer
    audioPlayerNode.port.postMessage({ command: "endOfAudio" });
    console.log("Sent 'endOfAudio' command to audio worklet.");
  }

  // Clear any audio data still buffered in app.js that hasn't been sent to the worklet yet
  audioBuffer = [];
  if (bufferTimer) {
    clearInterval(bufferTimer);
    bufferTimer = null;
    console.log("Cleared app.js audio buffer and buffer timer.");
  }
}

/**
 * =================================================================
 * Session Timeout Management
 * =================================================================
 */
function resetSessionTimeout() {
  // Clear existing timeout
  if (sessionTimeoutTimer) {
    clearTimeout(sessionTimeoutTimer);
  }

  // Update last activity time
  lastActivityTime = Date.now();

  // Set new timeout
  sessionTimeoutTimer = setTimeout(() => {
    console.log("Session timeout reached. Redirecting to initial page...");
    redirectToInitialPage();
  }, SESSION_TIMEOUT_DURATION);
}

function redirectToInitialPage() {
  // Clear session data
  localStorage.removeItem("adk-session-id");
  localStorage.removeItem("adk-user-id");

  // Close SSE connection
  if (eventSource) {
    eventSource.close();
    eventSource = null;
  }

  // Stop audio if active
  if (isAudioMode) {
    stopAudio();
    isAudioMode = false;
  }

  // Reset UI to initial state
  const landingContainer = document.getElementById("landingContainer");
  const mainLayout = document.getElementById("mainLayout");

  if (landingContainer && mainLayout) {
    mainLayout.style.display = "none";
    landingContainer.style.display = "flex";
  }

  // Clear messages and products
  if (messagesDiv) {
    messagesDiv.innerHTML = "";
  }
  resetToFullScreenView();

  // Reset suggested prompts state
  hideSuggestedPrompts();

  // Reset global state
  currentSessionId = null;
  currentUserId = null;

  console.log("Redirected to initial page due to inactivity");
  location.reload();
}

function trackUserActivity() {
  resetSessionTimeout();
}

/**
 * =================================================================
 * User ID and Session ID Management
 * =================================================================
 */
function generateAndStoreUserId() {
  let userId = localStorage.getItem("adk-user-id");
  if (!userId) {
    userId =
      "user-" +
      Math.random().toString(36).substring(2, 15) +
      Math.random().toString(36).substring(2, 15);
    localStorage.setItem("adk-user-id", userId);
  }
  currentUserId = userId;
  return userId;
}

/**
 * =================================================================
 * SSE (Server-Sent Events) Communication
 * =================================================================
 */
function connectSSE() {
  const userId = generateAndStoreUserId();
  currentUserId = userId;

  if (eventSource) {
    eventSource.close();
  }

  let url = `/events/${userId}?is_audio=${isAudioMode}`;
  const storedSessionId = localStorage.getItem("adk-session-id");
  if (storedSessionId) {
    url += `&sessionId=${storedSessionId}`;
  }

  eventSource = new EventSource(url);
  console.log("Connecting to SSE:", url);
  window.eventSource = eventSource; // For debugging

  eventSource.onopen = () => {
    console.log("SSE connection established.");
    sendButton.disabled = false;
    micButton.disabled = false;
    // Start session timeout when connection is established
    resetSessionTimeout();
  };

  eventSource.onmessage = (event) => {
    const message_from_server = JSON.parse(event.data);
    console.log("[AGENT TO CLIENT] ", message_from_server);

    // Track activity when receiving messages
    trackUserActivity();

    if (message_from_server.event_type === "session_init") {
      currentSessionId = message_from_server.sessionId;
      currentUserId = message_from_server.userId;
      localStorage.setItem("adk-session-id", currentSessionId);
      localStorage.setItem("adk-user-id", currentUserId);
      console.log(
        `Session initialized: User ID: ${currentUserId}, Session ID: ${currentSessionId}`
      );
      return;
    }

    if (message_from_server.event_type === "ui_state_change") {
      handleUIStateChange(message_from_server.data);
      return;
    }

    if (message_from_server.interrupted) {
      console.log("Interruption detected. Stopping agent audio playback.");
      stopAgentAudioPlayback();
      return;
    }

    if (message_from_server.mime_type === "audio/pcm") {
      if (audioPlayerNode) {
        try {
          const audioData = base64ToArray(message_from_server.data);
          audioPlayerNode.port.postMessage(audioData);
        } catch (e) {
          console.error("Error decoding or sending audio data:", e);
        }
      } else {
        console.warn("audioPlayerNode is not initialized. Cannot play audio.");
      }
    } else if (message_from_server.mime_type === "application/json") {
      const data = message_from_server.data;
      if (
        data.recipe ||
        data.drink_name ||
        (data.data && (data.data.recipe || data.data.drink_name))
      ) {
        console.log("Received recipe data.", data);
        displayRecipeData(data);
      } else {
        console.log("Received product recommendations.", data);
        displayRecommendedProducts(data);
      }
    } else if (message_from_server.mime_type === "text/plain") {
      // Check if the text message contains JSON data (like recipe)
      let messageData = message_from_server?.data;
      let isJsonMessage = false;

      console.log("Received text chunk:", messageData);

      // Check if this chunk contains JSON patterns
      if (
        messageData.trim().startsWith("{") ||
        messageData.includes('"status"') ||
        messageData.includes('"data"') ||
        messageData.includes('"recepe"') ||
        messageData.includes('"recipe"')
      ) {
        if (!isReceivingJson) {
          console.log("Starting JSON reception");
          isReceivingJson = true;
          // Add opening brace if missing
          jsonBuffer = messageData.trim().startsWith("{")
            ? messageData
            : "{" + messageData;
        } else {
          // We're in the middle of receiving JSON, accumulate it
          jsonBuffer += messageData;
        }
        console.log(
          "Accumulating JSON chunk, current buffer length:",
          jsonBuffer.length
        );
      } else if (isReceivingJson) {
        // We're in the middle of receiving JSON, accumulate it
        jsonBuffer += messageData;
        console.log(
          "Accumulating JSON chunk, current buffer length:",
          jsonBuffer.length
        );
      }

      // Try to parse the accumulated JSON buffer
      if (isReceivingJson) {
        try {
          const parsedData = JSON.parse(jsonBuffer);
          if (
            parsedData &&
            (parsedData.status === "success" ||
              parsedData.data ||
              parsedData.recepe ||
              parsedData.recipe)
          ) {
            console.log("Successfully parsed complete JSON:", parsedData);

            // Check if this is recipe data or product data
            if (
              parsedData.recepe ||
              parsedData.recipe ||
              (parsedData.data &&
                (parsedData.data.recepe || parsedData.data.recipe))
            ) {
              console.log("Received recipe data:", parsedData);
              displayRecipeData(parsedData);
            } else if (parsedData.data && parsedData.data.products) {
              console.log("Received product recommendations:", parsedData);
              displayRecommendedProducts(parsedData);
            } else {
              // It's JSON, but not a format we recognize. Treat as agent text.
              appendTranscriptionMessage(jsonBuffer, "agent");
            }

            isJsonMessage = true;
            // Reset JSON buffer
            jsonBuffer = "";
            isReceivingJson = false;
          }
        } catch (e) {
          // JSON is not complete yet, continue accumulating
          console.log("JSON not complete yet, continuing to accumulate...");
          isJsonHandled = true; // Mark as handled to prevent it from being displayed as text.
        }
      }

      // If not JSON or JSON is not complete, try to parse as standalone JSON
      if (!isJsonMessage && !isReceivingJson) {
        try {
          // Try to parse the entire message as JSON
          const parsedData = JSON.parse(messageData);
          if (
            parsedData &&
            (parsedData.status === "success" ||
              parsedData.data ||
              parsedData.recepe ||
              parsedData.recipe)
          ) {
            console.log(
              "Found complete JSON data in single message:",
              parsedData
            );
            displayRecommendedProducts(parsedData);
            isJsonMessage = true;
          }
        } catch (e) {
          // If that fails, try to find JSON within the text
          const jsonMatch = messageData.match(/\{.*\}/s);
          if (jsonMatch) {
            try {
              const parsedData = JSON.parse(jsonMatch[0]);
              if (
                parsedData &&
                (parsedData.status === "success" ||
                  parsedData.data ||
                  parsedData.recepe ||
                  parsedData.recipe)
              ) {
                console.log(
                  "Found embedded JSON data in text message:",
                  parsedData
                );
                displayRecommendedProducts(parsedData);
                isJsonMessage = true;
              }
            } catch (e2) {
              console.log("Failed to parse embedded JSON:", e2);
              isJsonMessage = false;
            }
          } else {
            console.log("No JSON found in text message");
            isJsonMessage = false;
          }
        }
      }

      // Check if message contains JSON even if not detected above
      if (!isJsonMessage) {
        // Additional check for JSON patterns that might be missed
        if (
          messageData.includes('{"status"') ||
          messageData.includes('"recepe"') ||
          messageData.includes('"recipe"') ||
          messageData.includes('"data"')
        ) {
          console.log("Found JSON pattern in message, treating as JSON data");
          try {
            // Try to extract and parse JSON
            let jsonStr = messageData;
            if (!jsonStr.trim().startsWith("{")) {
              jsonStr = "{" + jsonStr;
            }
            if (!jsonStr.trim().endsWith("}")) {
              jsonStr = jsonStr + "}";
            }

            const parsedData = JSON.parse(jsonStr);
            console.log("Parsed JSON from message:", parsedData);
            displayRecommendedProducts(parsedData);
            isJsonMessage = true;
          } catch (e) {
            console.log("Failed to parse as JSON, treating as text");
          }
        }
      }

      // Only display as text message if it's not JSON data
      if (!isJsonMessage) {
        // if (!window.currentAgentMessageElement) {
        //   // Use window.currentAgentMessageElement
        //   window.currentAgentMessageElement = document.createElement("div");
        //   window.currentAgentMessageElement.classList.add(
        //     "message",
        //     "agent-message"
        //   );
        //   messagesDiv.appendChild(window.currentAgentMessageElement);
        // }
        // window.currentAgentMessageElement.textContent += messageData;
        // messagesDiv.scrollTop = messagesDiv.scrollHeight;

        appendTranscriptionMessage(messageData, message_from_server.role);
      }
      // console.log("Received product recommendations1.", message_from_server);
    }

    if (message_from_server.turn_complete) {
      currentAgentMessageElement = null; // Reset for the next turn
      currentUserMessageElement = null; // Also reset user message element

      hideSearchingMessage(); // Hide searching message when turn is complete
      // Reset JSON buffer for next request
      jsonBuffer = "";
      isReceivingJson = false;
      console.log("Turn complete - reset JSON buffer");
    }

    messagesDiv.scrollTop = messagesDiv.scrollHeight;
  };

  eventSource.onerror = () => {
    console.error("SSE connection lost. Attempting to reconnect...");
    sendButton.disabled = true;
    micButton.disabled = true;
    eventSource.close();
    eventSource = null;

    setTimeout(() => {
      connectSSE();
    }, SSE_RECONNECT_INTERVAL);
  };
}

/**
 * =================================================================
 * Message Sending (Fetch API)
 * =================================================================
 */
async function sendMessage(message) {
  const userId = localStorage.getItem("adk-user-id");
  const sessionId = localStorage.getItem("adk-session-id");

  if (!userId || !sessionId) {
    console.error("Cannot send message: User ID or Session ID not found.");
    return;
  }

  const send_url = `/send/${userId}/${sessionId}`;
  try {
    const response = await fetch(send_url, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(message),
    });
    if (!response.ok) {
      console.error("Failed to send message:", response.statusText);
    }
  } catch (error) {
    console.error("Error sending message:", error);
  }
}

/**
 * =================================================================
 * UI Interaction Handlers
 * =================================================================
 */
function showMainAppLayout() {
  const landingContainer = document.getElementById("landingContainer");
  const mainLayout = document.getElementById("mainLayout");

  if (landingContainer) {
    landingContainer.style.display = "none";
  } else {
    console.error("landingContainer not found!");
  }

  if (mainLayout) {
    mainLayout.style.display = "flex";
  } else {
    console.error("mainLayout not found!");
  }

  // Start session timeout when main app is shown
  resetSessionTimeout();

  // Show suggested prompts when entering the main chat interface
  setTimeout(() => {
    showSuggestedPrompts();
  }, 500); // Small delay to ensure UI is fully rendered
}

// Auto-resize textarea functionality (global so it can be used by suggested prompts)
function autoResize() {
  messageInput.style.height = "auto";
  messageInput.style.height = Math.min(messageInput.scrollHeight, 120) + "px";
}

function addSubmitHandler() {
  const messageForm = document.getElementById("messageForm");

  // Add input event listener for auto-resize
  messageInput.addEventListener("input", autoResize);

  // Handle Enter key (submit) vs Shift+Enter (new line)
  messageInput.addEventListener("keydown", function (e) {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      messageForm.dispatchEvent(new Event("submit"));
    }
  });

  messageForm.onsubmit = async function (e) {
    e.preventDefault();
    const message = messageInput.value.trim();
    if (message) {
      // Track user activity when sending message
      trackUserActivity();

      addMessage(message, "user");
      messageInput.value = "";
      autoResize(); // Reset height after clearing
      showSearchingMessage(); // Show searching message immediately

      await sendMessage({ mime_type: "text/plain", data: message });
      console.log("[CLIENT TO AGENT] " + message);
    }
    return false;
  };
}

function addMessage(text, sender, isHTML = false) {
  const messageElement = document.createElement("div");
  messageElement.classList.add("message", `${sender}-message`);

  if (isHTML) {
    messageElement.innerHTML = text;
  } else {
    messageElement.textContent = text;
  }

  messagesDiv.appendChild(messageElement);
  messagesDiv.scrollTop = messagesDiv.scrollHeight;

  // Hide suggested prompts when any message is added
  hideSuggestedPrompts();
}

/**
 * =================================================================
 * Audio Handling
 * =================================================================
 */
async function startAudio() {
  const [node, ctx] = await startAudioPlayerWorklet();
  audioPlayerNode = node;
  audioPlayerContext = ctx;

  const [recorderNode, recorderCtx, stream] = await startAudioRecorderWorklet(
    audioRecorderHandler
  );
  audioRecorderNode = recorderNode;
  audioRecorderContext = recorderCtx;
  micStream = stream;
}

function stopAudio() {
  if (bufferTimer) {
    clearInterval(bufferTimer);
    bufferTimer = null;
  }
  if (micStream) {
    micStream.getTracks().forEach((track) => track.stop());
  }
  if (audioRecorderContext) {
    audioRecorderContext.close();
  }
  if (audioPlayerContext) {
    audioPlayerContext.close();
  }
  audioPlayerNode = audioRecorderNode = micStream = null;
}

function audioRecorderHandler(pcmData) {
  audioBuffer.push(new Uint8Array(pcmData));
  if (!bufferTimer) {
    bufferTimer = setInterval(sendBufferedAudio, AUDIO_BUFFER_SEND_INTERVAL);
  }
}

function sendBufferedAudio() {
  if (audioBuffer.length === 0) return;
  const totalLength = audioBuffer.reduce((acc, chunk) => acc + chunk.length, 0);
  const combinedBuffer = new Uint8Array(totalLength);
  let offset = 0;
  audioBuffer.forEach((chunk) => {
    combinedBuffer.set(chunk, offset);
    offset += chunk.length;
  });

  sendMessage({
    mime_type: "audio/pcm",
    data: arrayBufferToBase64(combinedBuffer.buffer),
  });
  audioBuffer = [];
}

/**
 * =================================================================
 * Product Display and Modal
 * =================================================================
 */
/**
 * Helper function to validate size values
 */
function isValidSizeValue(value) {
  if (value === null || value === undefined) return false;
  if (typeof value !== "string" && typeof value !== "number") return false;

  const stringValue = String(value).trim().toLowerCase();
  if (
    stringValue === "" ||
    stringValue === "string" ||
    stringValue === "null" ||
    stringValue === "none"
  ) {
    return false;
  }

  return true;
}

function showProductLoadingCards(count = 6, loadingType = "default") {
  hideKeyboard();

  // Ensure the right panel is visible and layout is split
  rightPanel.classList.add("show");
  leftPanel.classList.add("split-view");

  // Clear existing content but preserve any error messages
  const errorMessages = recommendedProductsDiv.querySelectorAll(".no-products");
  recommendedProductsDiv.innerHTML = "";
  // errorMessages.forEach(msg => recommendedProductsDiv.appendChild(msg));

  // // Add loading indicator text based on type
  // const loadingText = document.createElement("div");
  // loadingText.classList.add("loading-status-text");
  // loadingText.textContent = loadingType === "database_query" ?
  //   "Searching our inventory..." : "Loading products...";
  // recommendedProductsDiv.appendChild(loadingText);

  // Create loading skeleton cards
  for (let i = 0; i < count; i++) {
    const loadingCard = document.createElement("div");
    loadingCard.classList.add("product-card-loading");

    loadingCard.innerHTML = `
      <div class="loading-image"></div>
      <div class="loading-content">
        <div class="loading-category"></div>
        <div class="loading-title"></div>
        <div style="display: flex; justify-content: space-between; align-items: center; margin-top: 10px;">
          <div class="loading-price"></div>
          <div class="loading-button"></div>
        </div>
      </div>
    `;

    recommendedProductsDiv.appendChild(loadingCard);
  }
}

function clearProductLoadingCards() {
  const loadingCards = recommendedProductsDiv.querySelectorAll(
    ".product-card-loading"
  );
  loadingCards.forEach((card) => card.remove());
}

function displayRecommendedProducts(data) {
  rightPanelHeadline.textContent = "Product Gallery";

  console.log("Complete data received:", data);

  // Show loader immediately when products start processing
  showProductLoadingCards(6, "products");

  // Clear any existing timeout
  if (productLoaderTimeout) {
    clearTimeout(productLoaderTimeout);
  }

  // Always show loader for 2 seconds minimum
  productLoaderTimeout = setTimeout(() => {
    clearProductLoadingCards();
    renderActualProducts(data);
  }, 2000);
}

function renderActualProducts(data) {
  // Hide keyboard when products appear
  hideKeyboard();

  // Ensure the right panel is visible and layout is split
  rightPanel.classList.add("show");
  leftPanel.classList.add("split-view");

  // Clear any existing content (including loading cards)
  recommendedProductsDiv.innerHTML = "";

  // The dev_anas backend sends data in the format { "data": { "products": [...] } }
  const products =
    data && data.products
      ? data.products
      : data && data.data && data.data.products
      ? data.data.products
      : null;
  console.log(products, "products");

  if (!Array.isArray(products) || products.length === 0) {
    recommendedProductsDiv.innerHTML =
      '<p class="no-products">No products found matching your criteria.</p>';
    return;
  }

  products.forEach((product) => {
    const productCard = document.createElement("div");
    productCard.classList.add("product-card");

    const imageUrl =
      product.images && product.images.length > 0
        ? product.images[0].url
        : "/assets/no_image.png";

    // Use global utility functions to set category colors for high contrast
    const categoryColor = stringToColour(product.main_category);
    //const textColor = getContrastYIQ(categoryColor);
    productCard.style.setProperty("--category-label-color", categoryColor);
    //productCard.style.setProperty("--category-text-color", textColor);

    const sizeValue =
      product?.size ||
      product?.size?.sizeValue ||
      product?.packSizeDetail?.itemSizeValue;
    const hasValidSize = isValidSizeValue(sizeValue);
    const hasValidLocation = isValidSizeValue(product.location);

    // Use innerHTML for the static part of the card
    productCard.innerHTML = `
      <div class="product-image-container">
        <img src="${imageUrl}" alt="${
      product.name || product.item_name
    }" class="product-image">
      </div>
      <div class="product-details">
        <div class="product-header">
          <div class="product-category-label">${
            product.main_category || "Uncategorized"
          }</div>
        </div>
        <div class="product-content">
          <h3 class="product-title">${product.name || product.item_name}</h3>
          <div class="product-price">${
            product.sale_price !== null
              ? parseFloat(product.sale_price).toFixed(2)
              : "0.00"
          }</div>
          <div class="product-info-row">
            ${
              hasValidSize
                ? `<div class="product-size-info">${sizeValue}</div>`
                : ""
            }
            ${
              hasValidLocation
                ? `<div class="product-location-info">LOCATION: ${product.location}</div>`
                : ""
            }
          </div>
        </div>
        <div class="product-footer"></div>
      </div>
    `;

    // Create the button programmatically
    const productFooter = productCard.querySelector(".product-footer");
    const viewDetailsBtn = document.createElement("button");
    viewDetailsBtn.classList.add("view-details-btn");
    viewDetailsBtn.dataset.productUpc = product.id; // Set dataset property

    const hasImages =
      product.images &&
      Array.isArray(product.images) &&
      product.images.length > 0;

    if (!hasImages) {
      viewDetailsBtn.classList.add("hidden");
      viewDetailsBtn.disabled = true;
      viewDetailsBtn.title = "No images available for this product";
    }

    viewDetailsBtn.innerHTML = `
        <span>${hasImages ? "View Details" : "No Details Available"}</span>
        <div class="icon"></div>
    `;

    // Add event listener
    viewDetailsBtn.addEventListener("click", async (e) => {
      e.stopPropagation();
      trackUserActivity();
      const upc = e.currentTarget.dataset.productUpc;
      console.log("View Details Button Clicked!");
      console.log("UPC from dataset:", upc);
      console.log("Product object:", product);
      if (upc) {
        const originalText = viewDetailsBtn.querySelector("span").textContent;
        viewDetailsBtn.querySelector("span").textContent = "Loading...";
        viewDetailsBtn.disabled = true;
        viewDetailsBtn.style.opacity = "0.6";

        const productDetail = await fetchProductDetails(upc);
        console.log(productDetail);

        viewDetailsBtn.querySelector("span").textContent = originalText;
        viewDetailsBtn.disabled = false;
        viewDetailsBtn.style.opacity = "1";

        if (productDetail) {
          openProductModal(productDetail);
        } else {
          console.error("Failed to fetch product details for UPC:", upc);
          alert("Could not load product details.");
        }
      }
    });

    productFooter.appendChild(viewDetailsBtn);
    recommendedProductsDiv.appendChild(productCard);
  });
}

async function fetchProductDetails(upc) {
  console.log("fetchProductDetails called with:", { upc });

  if (!upc) {
    console.error("Missing UPC for product details fetch.");
    return null;
  }

  const url = `/product_details/${upc}`;
  console.log("Fetching product details from URL:", url);

  try {
    const response = await fetch(url);
    console.log("Response status:", response.status);
    console.log("Response ok:", response.ok);

    const responseText = await response.text();
    console.log("Raw response from backend:", responseText);

    if (!response.ok) {
      throw new Error(
        `HTTP error! status: ${response.status}, response: ${responseText}`
      );
    }

    const data = JSON.parse(responseText);
    console.log("Response data:", data);

    if (data.status === "success" && data.data) {
      return data.data;
    } else {
      console.error("Error fetching product details:", data.error || data);
      return null;
    }
  } catch (error) {
    console.error("Fetch error for product details:", error);
    return null;
  }
}

function openProductModal(product) {
  let modal = document.getElementById("productModal");
  if (!modal) {
    modal = createProductModal();
  }

  const modalTitle = document.getElementById("modalTitle");
  const modalBody = document.getElementById("modalBody");
  const productCategoryBadge = document.getElementById("productCategory");

  if (!modalTitle || !modalBody || !productCategoryBadge) {
    console.error("Modal elements not found!");
    return;
  }

  modalTitle.textContent = product.brandName || "Product Details";
  productCategoryBadge.textContent = product.main_category || "Category";

  // Use global utility functions to set category colors for high contrast in the modal
  const categoryName = product.main_category || "Category";
  const categoryColor = stringToColour(categoryName);
  const textColor = getContrastYIQ(categoryColor);
  productCategoryBadge.textContent = categoryName;
  productCategoryBadge.style.setProperty(
    "--category-label-color",
    categoryColor
  );
  productCategoryBadge.style.setProperty("--category-text-color", textColor);

  // Create image gallery HTML for left section
  const images = product.images || [];
  const hasImages = images.length > 0;
  const mainImageUrl = hasImages ? images[0].url : "/assets/no_image.png";
  console.log(images, "images");
  let imageGalleryHTML = "";
  if (hasImages) {
    imageGalleryHTML = `
      <div class="product-image-gallery">
        <div class="main-image-container">
          <img id="mainProductImage" src="${mainImageUrl}" alt="${
      product.name
    }" class="main-product-image">
        </div>
        ${
          images.length > 1
            ? `
          <div class="image-thumbnails">
            <button class="thumbnail-nav prev" data-direction="prev">&lt;</button>
            <div class="thumbnails-wrapper">
              <div class="thumbnails-container" id="thumbnailsContainer">
                ${images
                  .map(
                    (image, index) => `
                  <img src="${image.url}"
                       alt="${product.name} - Image ${index + 1}"
                       class="thumbnail-image ${index === 0 ? "active" : ""}"
                       data-image-url="${image.url}">
                `
                  )
                  .join("")}
              </div>
            </div>
            <button class="thumbnail-nav next" data-direction="next">&gt;</button>
          </div>
        `
            : ""
        }
      </div>
    `;
  } else {
    imageGalleryHTML = `
      <div class="product-image-gallery">
        <div class="main-image-container">
          <div class="no-image-placeholder">No Image Available</div>
        </div>
      </div>
    `;
  }

  // Get product insights
  const insights = product.productInsight || {};

  // Format price with 2 decimal places
  const formattedPrice =
    product.sale_price !== null && product.sale_price !== undefined
      ? parseFloat(product.sale_price).toFixed(2)
      : "0.00";

  // Create additional product details for left section
  let additionalDetailsHTML = "";

  // Add product specifications if available
  if (insights.specifications || product.specifications) {
    const specs = insights.specifications || product.specifications || {};
    if (typeof specs === "object" && Object.keys(specs).length > 0) {
      additionalDetailsHTML += `
        <div class="product-specifications">
          <h3 class="section-title">Specifications</h3>
          <div class="specs-grid">
      `;

      for (const [key, value] of Object.entries(specs)) {
        if (value) {
          additionalDetailsHTML += `
            <div class="spec-item">
              <span class="spec-label">${key}:</span>
              <span class="spec-value">${value}</span>
            </div>
          `;
        }
      }

      additionalDetailsHTML += `
          </div>
        </div>
      `;
    }
  }

  // // Add reviews summary if available
  // if (insights.rating) {
  //   additionalDetailsHTML += `
  //     <div class="reviews-summary">
  //       <h3 class="section-title">Rating Summary</h3>
  //       <div class="rating-bar-container">
  //         <div class="rating-overall">
  //           <span class="rating-number">${parseFloat(insights.rating).toFixed(
  //             1
  //           )}</span>
  //           <div class="rating-stars-small">
  //             ${generateStarRating(insights.rating || 0)}
  //           </div>
  //         </div>
  //       </div>
  //     </div>
  //   `;
  // }

  // Create pairing recommendations HTML
  const pairingHTML = `
    <div class="pairing-recommendations">
      <h4 class="pairing-title">Pairing Options</h4>
      <div class="pairing-content">
        <div class="pairing-item">
          <span class="pairing-text">${product.pairingInfo}</span>
        </div>
      </div>
    </div>
  `;

  modalBody.innerHTML = `
    <div class="modal-left-section">
      ${imageGalleryHTML}
      ${pairingHTML}
      ${additionalDetailsHTML}
    </div>
    <div class="modal-right-section">
      <div class="product-info">       
        <h1 class="product-title-large">${
          product.item_name || product.name || "Product Details"
        }</h1>
                ${
                  product.description || insights.description
                    ? `
          <div class="product-description-text">
            ${product.description || insights.description}
          </div>
        `
                    : ""
                }
        <div class="product-rating">
          ${generateStarRating(insights.rating || 0)}
        </div>
        <div class="product-price-large">$${formattedPrice}</div>


        <div class="selectors-container">
          ${
            insights.type
              ? `
            <div class="selector-group">
              <label class="selector-label">Type</label>
              <div class="selector-options">
                <div class="selector-option active">${insights.type}</div>
              </div>
            </div>
          `
              : `
            <div class="selector-group">
              <label class="selector-label">Type</label>
              <div class="selector-options">
                <div class="selector-option active">Not specified</div>
              </div>
            </div>
          `
          }

          ${
            insights.alcoholContent
              ? `
            <div class="selector-group">
              <label class="selector-label">Alcohol Content</label>
              <div class="selector-options">
                <div class="selector-option active">${insights.alcoholContent}</div>
              </div>
            </div>
          `
              : `
            <div class="selector-group">
              <label class="selector-label">Alcohol Content</label>
              <div class="selector-options">
                <div class="selector-option active">Not specified</div>
              </div>
            </div>
          `
          }

          ${
            insights.origin
              ? `
            <div class="selector-group">
              <label class="selector-label">Origin</label>
              <div class="selector-options">
                <div class="selector-option active">${insights.origin}</div>
              </div>
            </div>
          `
              : `
            <div class="selector-group">
              <label class="selector-label">Origin</label>
              <div class="selector-options">
                <div class="selector-option active">Not specified</div>
              </div>
            </div>
          `
          }

          <div class="selector-group">
            <label class="selector-label">Taste</label>
            <div class="selector-options">
              <div class="selector-option active">${
                insights.taste || "Not specified"
              }</div>
            </div>
          </div>

          <div class="selector-group">
            <label class="selector-label">Taste Profile</label>
            <div class="selector-options">
              <div class="selector-option active">${
                insights.tasteProfile || "Not specified"
              }</div>
            </div>
          </div>

          <div class="selector-group">
            <label class="selector-label">Ingredients</label>
            <div class="selector-options">
              <div class="selector-option active">${
                insights.ingredients || "Not specified"
              }</div>
            </div>
          </div>
        </div>

        <div class="product-meta">
          <div class="meta-item">
            <span class="meta-label">Famous In:</span>
            <span class="meta-value">${
              insights.famousIn || "Not specified"
            }</span>
          </div>
          <div class="meta-item">
            <span class="meta-label">Fun Fact:</span>
            <span class="meta-value">${
              insights.funFact || "Not specified"
            }</span>
          </div>
        </div>
      </div>
    </div>
  `;

  modal.classList.add("show");
  document.body.style.overflow = "hidden";

  // Add event listeners for image slider functionality after DOM is ready
  setTimeout(() => {
    setupImageSliderEventListeners();
  }, 100);

  // Also add event delegation as a fallback
  setupEventDelegation();
}

function createProductModal() {
  const modalHTML = `
        <div class="modal-overlay" id="productModal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2 class="modal-title" id="modalTitle">Recommended Products</h2>
                    <span class="product-category-badge" id="productCategory">Product Category</span>
                    <button type="button" class="close-btn">&times;</button>
                </div>
                <div class="modal-body" id="modalBody">
                    <!-- Product details will be loaded here -->
                </div>
            </div>
        </div>
    `;
  document.body.insertAdjacentHTML("beforeend", modalHTML);
  const modal = document.getElementById("productModal");
  const closeBtn = modal.querySelector(".close-btn");

  closeBtn.addEventListener("click", () => {
    trackUserActivity(); // Track activity on button click
    closeModal();
  });
  modal.addEventListener("click", function (e) {
    if (e.target === this) {
      trackUserActivity(); // Track activity on modal click
      closeModal();
    }
  });
  document.addEventListener("keydown", function (e) {
    if (e.key === "Escape") {
      trackUserActivity(); // Track activity on key press
      closeModal();
    }
  });
  return modal;
}

function closeModal() {
  const modal = document.getElementById("productModal");
  if (modal) {
    modal.classList.remove("show");
    document.body.style.overflow = "auto";
  }
}

function showSearchingMessage() {
  if (!window.searchingMessageElement) {
    // Use window.searchingMessageElement
    window.searchingMessageElement = document.createElement("div");
    searchingMessageElement.textContent = "Searching...";
    window.searchingMessageElement.classList.add(
      "message",
      "agent-message",
      "searching-message"
    );
    messagesDiv.appendChild(window.searchingMessageElement);
    messagesDiv.scrollTop = messagesDiv.scrollHeight;
  }
}

function hideSearchingMessage() {
  if (window.searchingMessageElement) {
    // Use window.searchingMessageElement
    window.searchingMessageElement.remove();
    window.searchingMessageElement = null;
  }
}

function displayRecipeData(data) {
  rightPanelHeadline.textContent = "Recipe Gallery";

  console.log("Displaying recipe data:", data);

  // Show loader for recipes
  showProductLoadingCards(4, "recipe");

  // Clear any existing timeout
  if (productLoaderTimeout) {
    clearTimeout(productLoaderTimeout);
  }

  // Always show loader for 2 seconds
  productLoaderTimeout = setTimeout(() => {
    clearProductLoadingCards();
    renderActualRecipe(data);
  }, 2000);
}

function renderActualRecipe(data) {
  // Hide keyboard when recipe appears
  hideKeyboard();

  // Ensure the right panel is visible and layout is split
  rightPanel.classList.add("show");
  leftPanel.classList.add("split-view");

  // Clear any existing content
  recommendedProductsDiv.innerHTML = "";

  // Extract recipe from the structured data
  const recipe = data?.data || data;

  if (
    recipe &&
    (recipe.drink_name || recipe.ingredients || recipe.instructions)
  ) {
    displayRecipeInPanel(recipe);
  } else {
    console.log("No recipe found in data");
    recommendedProductsDiv.innerHTML =
      '<p class="no-products">Recipe data received but could not be parsed.</p>';
  }
}

function displayRecipeInPanel(recipe) {
  console.log("Displaying recipe in panel:", recipe);

  // Create recipe card container
  const recipeCard = document.createElement("div");
  recipeCard.classList.add("recipe-card-panel");

  // Create recipe card HTML with structured data
  recipeCard.innerHTML = `
    <div class="recipe-card-header">
      <h3 class="recipe-card-title">${recipe.drink_name || "Recipe"}</h3>
      <div class="recipe-card-icon">🍹</div>
    </div>
    <div class="recipe-card-content">
      ${formatRecipeContent(recipe)}
    </div>
  `;

  recommendedProductsDiv.appendChild(recipeCard);
}

function formatRecipeContent(recipe) {
  let content = "";

  // Add description if available
  if (recipe.description) {
    content += `<div class="recipe-section">
      <p class="recipe-description">${recipe.description}</p>
    </div>`;
  }

  // Add glassware if available
  if (recipe.glassware) {
    content += `<div class="recipe-section">
      <h4>Glassware:</h4>
      <p>${recipe.glassware}</p>
    </div>`;
  }

  // Add ingredients
  if (recipe.ingredients) {
    content += '<div class="recipe-section"><h4>Ingredients:</h4>';
    if (Array.isArray(recipe.ingredients)) {
      content += "<ul>";
      recipe.ingredients.forEach((ingredient) => {
        content += `<li>${ingredient}</li>`;
      });
      content += "</ul>";
    } else {
      content += `<p>${recipe.ingredients}</p>`;
    }
    content += "</div>";
  }

  // Add instructions
  if (recipe.instructions) {
    content += '<div class="recipe-section"><h4>Instructions:</h4>';
    if (Array.isArray(recipe.instructions)) {
      content += "<ol>";
      recipe.instructions.forEach((instruction) => {
        content += `<li>${instruction}</li>`;
      });
      content += "</ol>";
    } else {
      content += `<p>${recipe.instructions}</p>`;
    }
    content += "</div>";
  }

  // Add notes if available
  if (recipe.notes) {
    content += `<div class="recipe-section">
      <h4>Notes:</h4>
      <p class="recipe-notes">${recipe.notes}</p>
    </div>`;
  }

  return content;
}

/**
 * Appends transcribed text to the appropriate message bubble (user or agent),
 * creating a new bubble if one for that role doesn't already exist for the current turn.
 * @param {string} text - The text to append.
 * @param {string} role - The role of the speaker ('user' or 'agent').
 */
function appendTranscriptionMessage(text, role) {
  let messageElement;

  if (role === "user") {
    if (!currentUserMessageElement) {
      currentUserMessageElement = document.createElement("div");
      currentUserMessageElement.classList.add("message", "user-message");
      messagesDiv.appendChild(currentUserMessageElement);
    }
    messageElement = currentUserMessageElement;
  } else {
    // Default to agent for 'agent' role or any other unspecified role
    if (!currentAgentMessageElement) {
      currentAgentMessageElement = document.createElement("div");
      currentAgentMessageElement.classList.add("message", "agent-message");
      messagesDiv.appendChild(currentAgentMessageElement);
    }
    messageElement = currentAgentMessageElement;
  }

  messageElement.textContent += text;
  messagesDiv.scrollTop = messagesDiv.scrollHeight;
}

function formatRecipeMessage(recipe) {
  let message = '<div class="recipe-container">';

  if (recipe.name) {
    message += `<h3 class="recipe-title">${recipe.name}</h3>`;
  }

  if (recipe.ingredients) {
    message +=
      '<div class="recipe-section"><h4 class="recipe-section-title">Ingredients:</h4>';
    if (Array.isArray(recipe.ingredients)) {
      message += '<ul class="recipe-list">';
      recipe.ingredients.forEach((ingredient) => {
        message += `<li>${ingredient}</li>`;
      });
      message += "</ul>";
    } else if (typeof recipe.ingredients === "string") {
      message += `<p class="recipe-text">${recipe.ingredients}</p>`;
    }
    message += "</div>";
  }

  if (recipe.instructions) {
    message +=
      '<div class="recipe-section"><h4 class="recipe-section-title">Instructions:</h4>';
    if (Array.isArray(recipe.instructions)) {
      message += '<ol class="recipe-list recipe-instructions">';
      recipe.instructions.forEach((instruction) => {
        message += `<li>${instruction}</li>`;
      });
      message += "</ol>";
    } else if (typeof recipe.instructions === "string") {
      message += `<p class="recipe-text">${recipe.instructions}</p>`;
    }
    message += "</div>";
  }

  if (recipe.steps) {
    message +=
      '<div class="recipe-section"><h4 class="recipe-section-title">Steps:</h4>';
    if (Array.isArray(recipe.steps)) {
      message += '<ol class="recipe-list recipe-steps">';
      recipe.steps.forEach((step) => {
        message += `<li>${step}</li>`;
      });
      message += "</ol>";
    } else if (typeof recipe.steps === "string") {
      message += `<p class="recipe-text">${recipe.steps}</p>`;
    }
    message += "</div>";
  }

  message += "</div>";
  return message;
}

function resetToFullScreenView() {
  rightPanel.classList.remove("show");
  leftPanel.classList.remove("split-view");
  recommendedProductsDiv.innerHTML = "";
  hideSearchingMessage();
}

/**
 * =================================================================
 * Utility Functions
 * =================================================================
 */

/**
 * Generates a consistent, deterministic hex color code from any given string.
 * @param {string} str The input string (e.g., a category name).
 * @returns {string} A hex color code (e.g., "#a1b2c3").
 */
function stringToColour(str) {
  if (!str) return "#6c757d"; // A neutral default color for empty/null strings
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    hash = str.charCodeAt(i) + ((hash << 5) - hash);
    hash = hash & hash; // Ensure 32bit integer
  }
  let colour = "#";
  for (let i = 0; i < 3; i++) {
    const value = (hash >> (i * 8)) & 0xff;
    colour += ("00" + value.toString(16)).substr(-2);
  }
  return colour;
}

/**
 * Determines whether black or white text is more readable on a given hex color background.
 * @param {string} hexcolor The background hex color (e.g., "#a1b2c3").
 * @returns {string} Either "#000000" (black) or "#ffffff" (white).
 */
function getContrastYIQ(hexcolor) {
  if (!hexcolor) return "#ffffff";
  const r = parseInt(hexcolor.substring(1, 3), 16);
  const g = parseInt(hexcolor.substring(3, 5), 16);
  const b = parseInt(hexcolor.substring(5, 7), 16);
  const yiq = (r * 299 + g * 587 + b * 114) / 1000;
  return yiq >= 128 ? "#000000" : "#ffffff";
}

function base64ToArray(base64) {
  let cleanedBase64 = base64.replace(/_/g, "/").replace(/-/g, "+");
  while (cleanedBase64.length % 4) {
    cleanedBase64 += "=";
  }
  try {
    const binaryString = window.atob(cleanedBase64);
    const len = binaryString.length;
    const bytes = new Uint8Array(len);
    for (let i = 0; i < len; i++) {
      bytes[i] = binaryString.charCodeAt(i);
    }
    return bytes.buffer;
  } catch (e) {
    console.error("Error in base64ToArray (after cleaning):", e);
    console.error("Problematic base64 string (original):", base64);
    console.error("Problematic base64 string (cleaned):", cleanedBase64);
    throw e;
  }
}

function arrayBufferToBase64(buffer) {
  let binary = "";
  const bytes = new Uint8Array(buffer);
  for (let i = 0; i < bytes.byteLength; i++) {
    binary += String.fromCharCode(bytes[i]);
  }
  return window.btoa(binary);
}

// Enhanced image switching function
function switchMainImage(imageUrl, thumbnailElement) {
  console.log("Switching main image to:", imageUrl);
  const mainImage = document.getElementById("mainProductImage");
  if (mainImage) {
    mainImage.style.opacity = "0.5";
    setTimeout(() => {
      mainImage.src = imageUrl;
      mainImage.style.opacity = "1";
      console.log("Main image updated successfully");
    }, 150);
  } else {
    console.error("Main image element not found!");
  }

  // Update active thumbnail
  const thumbnails = document.querySelectorAll(".thumbnail-image");
  thumbnails.forEach((thumb) => thumb.classList.remove("active"));
  if (thumbnailElement) {
    thumbnailElement.classList.add("active");
    console.log("Active thumbnail updated");
  }
}

// Enhanced thumbnail navigation function that changes main image and active thumbnail
function scrollThumbnails(direction) {
  console.log("Navigating images:", direction);
  const container = document.getElementById("thumbnailsContainer");
  if (!container) {
    console.error("Thumbnails container not found!");
    return;
  }

  const thumbnails = container.querySelectorAll(".thumbnail-image");
  if (thumbnails.length === 0) {
    console.error("No thumbnail images found!");
    return;
  }

  // Find currently active thumbnail
  let currentActiveIndex = -1;
  thumbnails.forEach((thumb, index) => {
    if (thumb.classList.contains("active")) {
      currentActiveIndex = index;
    }
  });

  // If no active thumbnail found, default to first one
  if (currentActiveIndex === -1) {
    currentActiveIndex = 0;
  }

  // Calculate new index based on direction
  let newActiveIndex;
  if (direction === "prev") {
    newActiveIndex =
      currentActiveIndex > 0 ? currentActiveIndex - 1 : thumbnails.length - 1;
  } else {
    newActiveIndex =
      currentActiveIndex < thumbnails.length - 1 ? currentActiveIndex + 1 : 0;
  }

  // Get the new thumbnail and its image URL
  const newActiveThumbnail = thumbnails[newActiveIndex];
  const newImageUrl = newActiveThumbnail.getAttribute("data-image-url");

  console.log(
    `Switching from index ${currentActiveIndex} to ${newActiveIndex}`
  );
  console.log("New image URL:", newImageUrl);

  // Switch the main image and update active thumbnail
  switchMainImage(newImageUrl, newActiveThumbnail);

  // Scroll the thumbnail container to show the active thumbnail
  const thumbnailWidth = newActiveThumbnail.offsetWidth + 10; // Add some margin
  const containerWidth = container.clientWidth;
  const scrollPosition =
    newActiveIndex * thumbnailWidth - containerWidth / 2 + thumbnailWidth / 2;

  container.scrollTo({
    left: Math.max(
      0,
      Math.min(scrollPosition, container.scrollWidth - containerWidth)
    ),
    behavior: "smooth",
  });

  console.log(`Image navigation completed. Active index: ${newActiveIndex}`);
}

// Setup event listeners for image slider functionality
function setupImageSliderEventListeners() {
  console.log("Setting up image slider event listeners...");

  // Wait a bit more to ensure DOM is fully rendered
  setTimeout(() => {
    // Add event listeners for thumbnail navigation buttons
    const navButtons = document.querySelectorAll(".thumbnail-nav");
    console.log("Found navigation buttons:", navButtons.length);

    navButtons.forEach((button) => {
      button.addEventListener("click", function (e) {
        e.preventDefault();
        e.stopPropagation();
        const direction = this.getAttribute("data-direction");
        console.log("Navigation button clicked:", direction);
        scrollThumbnails(direction);
      });
    });

    // Add event listeners for thumbnail images
    const thumbnailImages = document.querySelectorAll(".thumbnail-image");
    console.log("Found thumbnail images:", thumbnailImages.length);

    thumbnailImages.forEach((thumbnail, index) => {
      console.log(
        `Setting up listener for thumbnail ${index}:`,
        thumbnail.getAttribute("data-image-url")
      );

      thumbnail.addEventListener("click", function (e) {
        e.preventDefault();
        e.stopPropagation();
        const imageUrl = this.getAttribute("data-image-url");
        console.log("Thumbnail clicked:", imageUrl);
        switchMainImage(imageUrl, this);
      });

      // Also add hover effect for better UX (only for non-touch devices)
      if (!("ontouchstart" in window)) {
        thumbnail.addEventListener("mouseenter", function () {
          this.style.transform = "scale(1.05)";
        });

        thumbnail.addEventListener("mouseleave", function () {
          this.style.transform = "scale(1)";
        });
      }
    });

    // Setup touch/swipe functionality for main image
    setupMainImageTouchSlider();

    // Setup touch/swipe functionality for thumbnail container
    setupThumbnailTouchSlider();

    console.log("All event listeners attached successfully!");
  }, 200);
}

// Enhanced star rating HTML with proper decimal support
function generateStarRating(rating) {
  const maxStars = 5;
  const numericRating = parseFloat(rating) || 0;
  const fullStars = Math.floor(numericRating);
  const decimal = numericRating - fullStars;
  const hasHalfStar = decimal >= 0.25 && decimal < 0.75;
  const hasThreeQuarterStar = decimal >= 0.75;

  let starsHTML = `<div class="stars-container" data-rating="${numericRating}">`;

  // Add full stars
  for (let i = 0; i < fullStars; i++) {
    starsHTML += '<span class="star full-star">★</span>';
  }

  // Add partial star if needed
  if (hasThreeQuarterStar) {
    starsHTML += '<span class="star three-quarter-star">★</span>';
  } else if (hasHalfStar) {
    starsHTML += '<span class="star half-star">★</span>';
  } else if (decimal > 0 && decimal < 0.25) {
    starsHTML += '<span class="star quarter-star">★</span>';
  }

  // Add empty stars
  const filledStars =
    fullStars +
    (hasHalfStar || hasThreeQuarterStar || (decimal > 0 && decimal < 0.25)
      ? 1
      : 0);
  const remainingStars = maxStars - filledStars;
  for (let i = 0; i < remainingStars; i++) {
    starsHTML += '<span class="star empty-star">☆</span>';
  }

  starsHTML += `<span class="rating-value">(${numericRating.toFixed(
    1
  )})</span></div>`;
  return starsHTML;
}

// Quantity control function
function changeQuantity(delta) {
  const quantityDisplay = document.getElementById("quantityDisplay");
  if (quantityDisplay) {
    let currentQuantity = parseInt(quantityDisplay.textContent) || 1;
    currentQuantity = Math.max(1, currentQuantity + delta);
    quantityDisplay.textContent = currentQuantity;
  }
}

// Selector option handling
document.addEventListener("click", function (e) {
  if (e.target.classList.contains("selector-option")) {
    // Remove active class from siblings
    const siblings =
      e.target.parentElement.querySelectorAll(".selector-option");
    siblings.forEach((sibling) => sibling.classList.remove("active"));

    // Add active class to clicked option
    e.target.classList.add("active");
  }
});

/**
 * =================================================================
 * Touch/Swipe Functionality for Image Slider
 * =================================================================
 */

// Setup touch/swipe functionality for main image
function setupMainImageTouchSlider() {
  const mainImageContainer = document.querySelector(".main-image-container");
  if (!mainImageContainer) return;

  let startX = 0;
  let startY = 0;
  let isDragging = false;
  let startTime = 0;

  // Touch start
  mainImageContainer.addEventListener(
    "touchstart",
    (e) => {
      const touch = e.touches[0];
      startX = touch.clientX;
      startY = touch.clientY;
      startTime = Date.now();
      isDragging = true;

      // Add visual feedback
      mainImageContainer.style.transition = "none";
    },
    { passive: true }
  );

  // Touch move
  mainImageContainer.addEventListener(
    "touchmove",
    (e) => {
      if (!isDragging) return;

      const touch = e.touches[0];
      const deltaX = touch.clientX - startX;
      const deltaY = touch.clientY - startY;

      // If horizontal swipe is more significant than vertical, prevent scrolling
      if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > 10) {
        e.preventDefault();
      }
    },
    { passive: false }
  );

  // Touch end
  mainImageContainer.addEventListener(
    "touchend",
    (e) => {
      if (!isDragging) return;

      const touch = e.changedTouches[0];
      const deltaX = touch.clientX - startX;
      const deltaY = touch.clientY - startY;
      const deltaTime = Date.now() - startTime;

      // Reset transition
      mainImageContainer.style.transition = "";
      isDragging = false;

      // Check if it's a swipe (minimum distance and not too slow)
      const minSwipeDistance = 50;
      const maxSwipeTime = 500;

      if (
        Math.abs(deltaX) > minSwipeDistance &&
        Math.abs(deltaX) > Math.abs(deltaY) &&
        deltaTime < maxSwipeTime
      ) {
        if (deltaX > 0) {
          // Swipe right - go to previous image
          scrollThumbnails("prev");
        } else {
          // Swipe left - go to next image
          scrollThumbnails("next");
        }
      }
    },
    { passive: true }
  );
}

// Setup touch/swipe functionality for thumbnail container
function setupThumbnailTouchSlider() {
  const thumbnailContainer = document.getElementById("thumbnailsContainer");
  if (!thumbnailContainer) return;

  let startX = 0;
  let scrollLeft = 0;
  let isDragging = false;
  let hasMoved = false;

  // Touch start
  thumbnailContainer.addEventListener(
    "touchstart",
    (e) => {
      const touch = e.touches[0];
      startX = touch.clientX;
      scrollLeft = thumbnailContainer.scrollLeft;
      isDragging = true;
      hasMoved = false;

      // Add visual feedback
      thumbnailContainer.style.scrollBehavior = "auto";
    },
    { passive: true }
  );

  // Touch move
  thumbnailContainer.addEventListener(
    "touchmove",
    (e) => {
      if (!isDragging) return;

      const touch = e.touches[0];
      const deltaX = touch.clientX - startX;

      // Scroll the container
      thumbnailContainer.scrollLeft = scrollLeft - deltaX;

      // Mark that we've moved
      if (Math.abs(deltaX) > 5) {
        hasMoved = true;
      }
    },
    { passive: true }
  );

  // Touch end
  thumbnailContainer.addEventListener(
    "touchend",
    () => {
      if (!isDragging) return;

      isDragging = false;

      // Restore smooth scrolling
      setTimeout(() => {
        thumbnailContainer.style.scrollBehavior = "smooth";
      }, 100);
    },
    { passive: true }
  );

  // Prevent click events on thumbnails if we've been dragging
  thumbnailContainer.addEventListener(
    "click",
    (e) => {
      if (hasMoved) {
        e.preventDefault();
        e.stopPropagation();
      }
    },
    true
  );
}

/**
 * =================================================================
 * Keyboard Management
 * =================================================================
 */

// Hide virtual keyboard by blurring the input field
function hideKeyboard() {
  if (messageInput) {
    messageInput.blur();

    // Additional method for mobile devices
    if (document.activeElement) {
      document.activeElement.blur();
    }

    // Force focus to a non-input element to ensure keyboard closes
    const tempElement = document.createElement("button");
    tempElement.style.position = "absolute";
    tempElement.style.left = "-9999px";
    tempElement.style.opacity = "0";
    document.body.appendChild(tempElement);
    tempElement.focus();
    setTimeout(() => {
      document.body.removeChild(tempElement);
    }, 100);
  }
}

/**
 * =================================================================
 * Fullscreen Functionality
 * =================================================================
 */

// Toggle fullscreen mode
function toggleFullscreen() {
  if (!document.fullscreenElement) {
    // Enter fullscreen
    document.documentElement
      .requestFullscreen()
      .then(() => {
        updateFullscreenIcon(true);
      })
      .catch((err) => {
        console.error("Error attempting to enable fullscreen:", err);
      });
  } else {
    // Exit fullscreen
    document
      .exitFullscreen()
      .then(() => {
        updateFullscreenIcon(false);
      })
      .catch((err) => {
        console.error("Error attempting to exit fullscreen:", err);
      });
  }
}

// Update fullscreen icon based on state
function updateFullscreenIcon(isFullscreen) {
  if (fullscreenIcon) {
    if (isFullscreen) {
      fullscreenIcon.className = "fas fa-compress";
      fullscreenBtn.title = "Exit Fullscreen";
    } else {
      fullscreenIcon.className = "fas fa-expand";
      fullscreenBtn.title = "Enter Fullscreen";
    }
  }
}

// Listen for fullscreen changes (including F11, ESC key, etc.)
function handleFullscreenChange() {
  const isFullscreen = !!document.fullscreenElement;
  updateFullscreenIcon(isFullscreen);
}

// Make functions globally accessible
window.switchMainImage = switchMainImage;
window.scrollThumbnails = scrollThumbnails;
window.changeQuantity = changeQuantity;
window.showProductLoadingCards = showProductLoadingCards;
window.clearProductLoadingCards = clearProductLoadingCards;
window.toggleFullscreen = toggleFullscreen;
window.hideKeyboard = hideKeyboard;

/**
 * =================================================================
 * Global Activity Tracking
 * =================================================================
 */
function setupGlobalActivityTracking() {
  // Track mouse movements, clicks, and keyboard activity
  const activityEvents = [
    "mousedown",
    "mousemove",
    "keypress",
    "scroll",
    "touchstart",
    "click",
  ];

  activityEvents.forEach((event) => {
    document.addEventListener(event, trackUserActivity, true);
  });

  console.log("Global activity tracking setup complete");
}

/**
 * =================================================================
 * Initialization
 * =================================================================
 */
document.addEventListener("DOMContentLoaded", function () {
  // Setup global activity tracking
  setupGlobalActivityTracking();

  addSubmitHandler();

  // Initialize suggested prompts functionality
  initializeSuggestedPrompts();

  // Start Chat button
  if (startChatBtn) {
    startChatBtn.addEventListener("click", () => {
      trackUserActivity(); // Track activity on button click
      showMainAppLayout();
    });
  }

  // Fullscreen button
  if (fullscreenBtn) {
    fullscreenBtn.addEventListener("click", () => {
      trackUserActivity(); // Track activity on button click
      toggleFullscreen();
    });
  }

  // Listen for fullscreen changes (F11, ESC, etc.)
  document.addEventListener("fullscreenchange", handleFullscreenChange);
  document.addEventListener("webkitfullscreenchange", handleFullscreenChange);
  document.addEventListener("mozfullscreenchange", handleFullscreenChange);
  document.addEventListener("MSFullscreenChange", handleFullscreenChange);

  micButton.addEventListener("click", () => {
    trackUserActivity(); // Track activity on button click
    if (isAudioMode) {
      stopAudio();
      micButton.classList.remove("active");
      // Change icon back to microphone
      micButton.querySelector("i").className = "fas fa-microphone";
      listeningIndicator.style.display = "none";
      isAudioMode = false;
    } else {
      micButton.classList.add("active");
      // Change icon to stop
      micButton.querySelector("i").className = "fas fa-stop";
      listeningIndicator.style.display = "flex";
      isAudioMode = true;

      startAudio()
        .then(() => {
          connectSSE();
        })
        .catch((error) => {
          console.error("Error starting audio:", error);
          micButton.classList.remove("active");
          // Reset icon back to microphone on error
          micButton.querySelector("i").className = "fas fa-microphone";
          listeningIndicator.style.display = "none";
          isAudioMode = false;
        });
    }
  });

  // Initial connection for text mode
  connectSSE();
});

// Add new function for UI state management
function handleUIStateChange(uiState) {
  console.log("UI State Change:", uiState);

  if (uiState.error) {
    clearProductLoadingCards();
    if (recommendedProductsDiv.children.length === 0) {
      recommendedProductsDiv.innerHTML = `<p class="no-products">Search failed: ${uiState.error}</p>`;
    }
    return;
  }

  if (uiState.loading_products) {
    const loadingType = uiState.loading_type || "default";

    // Show loader immediately
    showProductLoadingCards(
      loadingType === "database_query" ? 8 : 6,
      loadingType
    );
    console.log(`Started loading cards for: ${loadingType}`);

    // Always keep loader for 2 seconds minimum
    setTimeout(() => {
      clearProductLoadingCards();
    }, 2000);
  } else if (uiState.loading_products === false) {
    // Don't clear immediately - let the 2 second timer handle it
    // This prevents premature clearing
  }
}
