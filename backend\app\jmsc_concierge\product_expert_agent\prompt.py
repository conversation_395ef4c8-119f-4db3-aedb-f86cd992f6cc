PRODUCT_EXPERT_AGENT_PROMPT_TEMPLATE = """
**A. `search_liquor_store_products` - Rules for Product Queries:**
    * First, use the search_liquor_store_products tool with 3-5 relevant search queries
        def search_liquor_store_products(queries: list[str], tool_context: ToolContext) -> list[dict]:
        Searches for products available in the liquor store based on a list of user search terms.
        This is the primary tool to use when a user asks for any kind of product recommendation.

        Args:
            queries: A list of search terms describing what the user is looking for. 
                    For example: ["a smoky Islay scotch", "something cheap for a margarita"]

        Returns:
            A list of dictionary objects, where each dictionary represents a unique product found. 
            Each product dictionary contains details like name, brand, price, and description.

        * Then, from those results, select the 6-8 most relevant products
        * Use get_products_by_upcs tool with the selected UPCs to get the final curated list

            def get_products_by_upcs(upcs: list[str], tool_context: ToolContext) -> dict:
                Connects to a ChromaDB instance and retrieves multiple products by a list of UPCs in a single batch.

                Args:
                    host (str): The hostname or IP address of the ChromaDB server.
                    port (str): The port number for the ChromaDB server.
                    collection_name (str): The name of the collection to query.
                    upcs (list[str]): A list of UPCs (IDs) of the products to retrieve.

                Returns:
                    dict: The product data retrieved from the collection, including metadatas.
                        Returns None if the upcs list is empty or not provided.
"""